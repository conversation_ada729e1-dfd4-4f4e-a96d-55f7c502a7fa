<script setup lang="tsx">
import { computed, onMounted, shallowRef, ref, watch } from 'vue';
import VirtualizedTable from '@/components/common/VirtualizedTable.vue';
import type { AccountInfo, TableOrderInfo, ColumnDefinition } from '@/types';
import { Utils } from '@/script';
import { RecordService } from '@/api';

// 响应式数据
const orders = shallowRef<TableOrderInfo[]>([]);
const account = shallowRef<AccountInfo | null>({
  id: 1,
  accountId: ***********,
  accountName: '银河期货1901',
  financeAccount: 'n31806',
  assetType: 1,
  available: ********.59,
  balance: 1********.57,
  buyMarketValue: ********.32,
  closeProfit: 90.38,
  commission: 12975.47,
  connectionStatus: true,
  credit: false,
  dayProfit: 81276.23,
  fundId: 1,
  fundName: '基金A',
  fundShare: 1.0,
  frozenMargin: 0,
  frozenCommission: 0,
  inMoney: 0,
  loanBuyBalance: 0,
  loanSellBalance: 0,
  loanSellQuota: 0,
  marketValue: ********.05,
  margin: 0,
  maxLimitMoney: ********.59,
  nav: 1.0238,
  outMoney: 0,
  positionProfit: 0,
  preBalance: ********.57,
  risePercent: 0.0992,
  sellMarketValue: 0,
  stockIndexMarketValue: ********.05,
  tradingDay: '2023-01-01',
  enableCreditBuy: 0,
});

const accountName = computed(() => {
  if (account.value) {
    return `${account.value.accountId} ${account.value.accountName}`;
  } else {
    return '--';
  }
});

// 账号详情字段配置
const accountFields = computed(() => {
  return [
    {
      label: '可用资金比例',
      value: !account.value
        ? '--'
        : Utils.formatNumber(account.value.available / account.value.balance, { percent: true }),
      color: '',
    },
    {
      label: '仓位比例',
      value: !account.value
        ? '--'
        : Utils.formatNumber(account.value.marketValue / account.value.balance, { percent: true }),
      color: '',
    },
    {
      label: '可用',
      value: !account.value
        ? '--'
        : Utils.formatNumber(account.value.available, { separator: true }),
      color: '',
    },
    {
      label: '总资产',
      value: !account.value ? '--' : Utils.formatNumber(account.value.balance, { separator: true }),
      color: '',
    },
    {
      label: '总市值',
      value: !account.value
        ? '--'
        : Utils.formatNumber(account.value.marketValue, { separator: true }),
      color: '',
    },
    {
      label: '盈亏',
      value: !account.value
        ? '--'
        : Utils.formatNumber(account.value.dayProfit, { separator: true }),
      color: !account.value ? '' : account.value.dayProfit >= 0 ? 'c-[--g-red]' : 'c-[--g-green]',
    },
  ];
});

// 委托列表表格列定义
const orderColumns: ColumnDefinition<TableOrderInfo> = [
  {
    key: 'instrument',
    dataKey: 'instrument',
    title: '证券代码',
    width: 100,
  },
  {
    key: 'instrumentName',
    dataKey: 'instrumentName',
    title: '证券名称',
    width: 120,
  },
  {
    key: 'volumeOriginal',
    dataKey: 'volumeOriginal',
    title: '仓位',
    width: 80,
    cellRenderer: ({ cellData }) => (
      <span>{Utils.formatNumber(cellData, { separator: true })}</span>
    ),
  },
  {
    key: 'tradedAmount',
    dataKey: 'tradedAmount',
    title: '全撤',
    width: 100,
    cellRenderer: ({ cellData }) => (
      <el-button size="small" color="var(--g-red)">
        全撤/F1
      </el-button>
    ),
  },
  {
    key: 'orderPrice',
    dataKey: 'orderPrice',
    title: '委托1',
    width: 80,
    cellRenderer: ({ cellData }) => (
      <div class="flex aic">
        <el-checkbox></el-checkbox>
        <span class="ml-10">--</span>
      </div>
    ),
  },
  {
    key: 'tradedPrice',
    dataKey: 'tradedPrice',
    title: '委托2',
    width: 80,
    cellRenderer: ({ cellData }) => (
      <div class="flex aic">
        <el-checkbox></el-checkbox>
        <span class="ml-10">--</span>
      </div>
    ),
  },
  {
    key: 'commission',
    dataKey: 'commission',
    title: '委托3',
    width: 80,
    cellRenderer: ({ cellData }) => (
      <div class="flex aic">
        <el-checkbox></el-checkbox>
        <span class="ml-10">--</span>
      </div>
    ),
  },
];

// 获取委托列表
const fetchOrders = async () => {
  const { errorCode, data } = await RecordService.getOrders(account.value!.accountId);
  if (errorCode === 0 && data) {
    orders.value = data;
  }
};

// 组件挂载时获取数据
onMounted(() => {
  if (account.value) {
    fetchOrders();
  }
});
</script>

<template>
  <div flex="~ col" h-full>
    <!-- 委托标题 -->
    <div h-32 flex aic px-16 border-b="1 solid [--el-border-color]">
      <span text-16 font-bold>委托</span>
    </div>

    <!-- 账号详情区域 -->
    <div p-16 border-b="1 solid [--el-border-color]">
      <!-- 账号选择 -->
      <div flex aic gap-8 mb-12>
        <div>账号</div>
        <div c-white>
          {{ accountName }}
        </div>
      </div>

      <!-- 账号详情字段 -->
      <div flex gap-24>
        <div v-for="field in accountFields" :key="field.label" flex="~ col" gap-4>
          <span>{{ field.label }}</span>
          <span :class="field.color || 'text-white'">{{ field.value }}</span>
        </div>
      </div>
    </div>

    <div px-16 h-32 flex aic flex-justify-end>
      <el-button size="small">撤勾选</el-button>
    </div>
    <!-- 委托列表 -->
    <div flex-1 min-h-1>
      <VirtualizedTable select :columns="orderColumns" :data="orders" />
    </div>
  </div>
</template>

<style scoped></style>
