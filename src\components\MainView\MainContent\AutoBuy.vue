<script setup lang="ts">
import MonitorPool from './AutoBuy/MonitorPool.vue';
import StrategyLogList from './AutoBuy/StrategyLogList.vue';
import OrderTable from './AutoBuy/OrderTable.vue';
import PositionTable from './AutoBuy/PositionTable.vue';
import OrderBook from './AutoBuy/OrderBook.vue';
import { computed, useTemplateRef } from 'vue';

const positionTableRef = useTemplateRef('positionTableRef');

const positionTableStyle = computed(() => {
  if (positionTableRef.value?.expanded) {
    return {
      flex: '1 1 0%',
    };
  } else {
    return {
      flex: '0 0 0px',
    };
  }
});
</script>

<template>
  <div flex p-10 gap-10>
    <div flex-1 min-w-1 flex="~ col" gap-10>
      <MonitorPool h="50%" bg="[--g-panel-bg]" />
      <OrderTable flex-1 min-h-1 bg="[--g-panel-bg]" />
    </div>
    <div w-400 flex="~ col" bg="[--g-panel-bg]">
      <OrderBook />
      <StrategyLogList flex-1 min-h-1 />
    </div>
    <PositionTable ref="positionTableRef" :style="positionTableStyle" min-w-1 bg="[--g-panel-bg]" />
  </div>
</template>

<style scoped></style>
