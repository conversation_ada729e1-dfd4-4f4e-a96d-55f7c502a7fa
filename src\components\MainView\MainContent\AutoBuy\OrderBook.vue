<script setup lang="ts">
import { computed, shallowRef, watch } from 'vue';
import { useStockSelectionStore } from '@/stores/counter';
import { TickService } from '@/api';
import type { StandardTick } from '@/types';
import { Utils } from '@/script';

const stockSelectionStore = useStockSelectionStore();
const tickData = shallowRef<StandardTick | null>(null);

// 获取行情数据
const fetchTickData = async (instrument: string) => {
  const { err, data } = await TickService.getTick(instrument);
  if (err === 0 && data) {
    tickData.value = data;
  }
};

// 监听选中股票变化
watch(
  () => stockSelectionStore.selectedStock,
  newStock => {
    if (newStock) {
      fetchTickData(newStock.instrument);
    } else {
      tickData.value = null;
    }
  },
  { immediate: true },
);

// 计算涨跌幅和涨跌额
const priceChange = computed(() => {
  if (!tickData.value) return { amount: 0, percent: 0 };
  const amount = tickData.value.lastPrice - tickData.value.preClosePrice;
  const percent = amount / tickData.value.preClosePrice;
  return { amount, percent };
});

// 获取价格颜色
const getPriceColor = (price: number, comparePrice: number) => {
  if (price > comparePrice) return 'c-[var(--g-red)]';
  if (price < comparePrice) return 'c-[var(--g-green)]';
  return 'c-white';
};
</script>

<template>
  <div flex="~ col" h-300 bg="[--g-panel-bg]">
    <!-- 股票信息 -->
    <div
      v-if="stockSelectionStore.selectedStock && tickData"
      p-12
      border-b="1 solid [--el-border-color]"
    >
      <div flex aic gap-8 mb-6>
        <span text-14 font-bold>{{ stockSelectionStore.selectedStock.instrument }}</span>
        <span text-12 c-gray>{{ stockSelectionStore.selectedStock.instrumentName }}</span>
      </div>
      <div flex aic gap-12>
        <span text-16 font-bold :class="getPriceColor(tickData.lastPrice, tickData.preClosePrice)">
          {{ Utils.formatNumber(tickData.lastPrice, { fix: 2 }) }}
        </span>
        <span text-11 :class="getPriceColor(priceChange.amount, 0)">
          {{ Utils.formatNumber(priceChange.amount, { fix: 2, prefix: true }) }}
        </span>
        <span text-11 :class="getPriceColor(priceChange.percent, 0)">
          {{ Utils.formatNumber(priceChange.percent, { percent: true, prefix: true }) }}
        </span>
      </div>
    </div>

    <!-- 买卖盘 -->
    <div v-if="tickData" flex-1 px-12 py-8>
      <!-- 表头 -->
      <div flex aic jcsb text-11 c-gray mb-4 px-4>
        <span>档位</span>
        <span>价格</span>
        <span>数量</span>
      </div>

      <div flex="~ col" gap-1>
        <!-- 卖盘 -->
        <div
          v-for="(price, index) in tickData.askPrice.slice(0, 5).reverse()"
          :key="`ask-${index}`"
          flex
          aic
          jcsb
          text-11
          h-18
          px-4
          hover:bg="[--el-border-color-lighter]"
        >
          <span class="c-[var(--g-green)]">卖{{ 5 - index }}</span>
          <span class="c-[var(--g-green)]">{{ Utils.formatNumber(price, { fix: 2 }) }}</span>
          <span c-white>{{ tickData.askVolume[4 - index] }}</span>
        </div>

        <!-- 分隔线 -->
        <div h-1 bg="[--el-border-color]" my-2></div>

        <!-- 买盘 -->
        <div
          v-for="(price, index) in tickData.bidPrice.slice(0, 5)"
          :key="`bid-${index}`"
          flex
          aic
          jcsb
          text-11
          h-18
          px-4
          hover:bg="[--el-border-color-lighter]"
        >
          <span class="c-[var(--g-red)]">买{{ index + 1 }}</span>
          <span class="c-[var(--g-red)]">{{ Utils.formatNumber(price, { fix: 2 }) }}</span>
          <span c-white>{{ tickData.bidVolume[index] }}</span>
        </div>
      </div>
    </div>

    <!-- 无数据提示 -->
    <div v-else flex-1 flex aic jcc c-gray>
      <span>请选择股票查看盘口信息</span>
    </div>
  </div>
</template>

<style scoped></style>
