<script setup lang="ts">
import { Misc } from '@/script';
import router from '@/router';

const signOut = () => {
  Misc.setUser();
  router.push({ name: 'login' });
};

const start = () => {
  console.log('start');
};

const stop = () => {
  console.log('stop');
};

const setting = () => {
  console.log('setting');
};
</script>

<template>
  <div px-10 h-40 flex aic jcsb bg="[--g-panel-bg]">
    <div>量化打板1.0</div>
    <div>
      <el-button size="small" @click="start">
        <i mr-4 fs-14 i-mdi-motion-play-outline />
        全局启动
      </el-button>
      <el-button size="small" @click="stop">
        <i mr-4 fs-14 i-mdi-motion-pause-outline />
        全局停止
      </el-button>
    </div>
    <div>
      <el-button size="small" @click="setting">
        <i mr-4 fs-14 i-mdi-cog-outline />
        设置
      </el-button>
      <el-button size="small" @click="signOut">
        <i mr-4 fs-14 i-mdi-logout-variant />
        退出
      </el-button>
    </div>
  </div>
</template>

<style scoped></style>
