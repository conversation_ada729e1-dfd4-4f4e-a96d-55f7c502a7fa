import type { InstrumentStatusEnum } from '@/enum';

/** 股票池详情 */
export interface PoolDetail {
  id: number;
  /** 证券代码 */
  instrument: string;
  /** 证券名称 */
  instrumentName: string;
  /** 状态 */
  status: InstrumentStatusEnum;
  /** 涨跌幅 */
  risePercent: number;
  /** 股票池名称 */
  poolName: string;
  /** 股票池ID */
  poolId: number;
  /** 仓位 */
  position: number;
}

/** 股票池 */
export interface Pool {
  id: number;
  /** 股票池名称 */
  name: string;
  /** 状态 */
  status: number;
  /** 类型 */
  type: number;
  /** 个股数量 */
  stockCount: number;
  /** 仓位 */
  position: number;
  /** 详情 */
  details: PoolDetail[];
}

/** 策略日志 */
export interface StrategyLog {
  /** 触发时间 */
  triggerTime: string;
  /** 触发类型 */
  triggerType: string;
  /** 触发内容 */
  triggerContent: string;
  /** 触发股票 */
  triggerStock: string;
  /** 买入金额 */
  amount: number;
}
