import { ref, computed } from 'vue';
import { defineStore } from 'pinia';
import type { PoolDetail } from '@/types';

export const useCounterStore = defineStore('counter', () => {
  const count = ref(0);
  const doubleCount = computed(() => count.value * 2);
  function increment() {
    count.value++;
  }

  return { count, doubleCount, increment };
});

// 股票选择状态管理
export const useStockSelectionStore = defineStore('stockSelection', () => {
  const selectedStock = ref<PoolDetail | null>(null);

  const setSelectedStock = (stock: PoolDetail | null) => {
    selectedStock.value = stock;
  };

  return {
    selectedStock,
    setSelectedStock,
  };
});
