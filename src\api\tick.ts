import http from './http';
import type { StandardTick } from '@/types';

class TickService {
  /**
   * 订阅合约行情
   * @param instrument - 合约代码
   * @param callback - 回调函数
   */
  static subscribeTick(instrument: string, callback: () => void): void {}

  /**
   * 退订合约行情
   * @param instrument - 合约代码
   */
  static unsubscribeTick(instrument: string): void {}

  /**
   * 获取合约行情数据
   * @param instrument - 合约代码
   */
  static getTick(instrument: string) {
    return http<StandardTick>('/api/tick', {
      method: 'GET',
      params: {
        instrument,
      },
    });
  }
}

export default TickService;
