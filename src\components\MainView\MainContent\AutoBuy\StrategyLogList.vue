<script setup lang="ts">
import type { StrategyLog } from '@/types';
import { shallowRef, onMounted } from 'vue';

const logs = shallowRef<StrategyLog[]>([]);

// 获取策略日志数据
const fetchStrategyLogs = async () => {
  try {
    const response = await fetch('/api/strategy-logs');
    const result = await response.json();
    if (result.err === 0 && result.data) {
      logs.value = result.data;
    }
  } catch (error) {
    console.error('获取策略日志失败:', error);
  }
};

// 格式化金额显示
const formatAmount = (amount: number) => {
  if (amount >= 10000) {
    return `${(amount / 10000).toFixed(0)}万`;
  }
  return `${amount}元`;
};

onMounted(() => {
  fetchStrategyLogs();
});
</script>

<template>
  <div flex="~ col">
    <div h-32 flex aic px-16 border-b="1 solid [--el-border-color]">
      <span text-16 font-bold>策略日志</span>
    </div>
    <div flex-1 min-h-1>
      <el-scrollbar>
        <!-- 策略日志列表 -->
        <div v-if="logs.length === 0" flex aic jcc h-full c-gray>
          <span>暂无策略日志</span>
        </div>
        <div v-else p-12 flex="~ col" gap-8>
          <div
            v-for="log in logs"
            :key="log.triggerTime"
            p-8
            mb-6
            bg="[--g-panel-bg2]"
            rounded-4
            leading-relaxed
            border-l="3 solid [--g-primary]"
          >
            <div c-white mb-2>
              [{{ log.triggerTime }}] 股票【{{ log.triggerStock }}】{{ log.triggerType }}，{{
                log.triggerContent
              }}，{{ log.triggerContent.includes('买入') ? '买入' : '卖出' }}金额为{{
                formatAmount(log.amount)
              }}
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<style scoped></style>
