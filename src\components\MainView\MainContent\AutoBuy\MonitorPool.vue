<script setup lang="tsx">
import type { ColumnDefinition, PoolDetail, RowAction } from '@/types';
import VirtualizedTable from '@/components/common/VirtualizedTable.vue';
import { shallowRef, ref, onMounted } from 'vue';
import { Misc, Utils } from '@/script';
import { ElMessage, ElMessageBox } from 'element-plus';
import { PoolService } from '@/api';
import { InstrumentStatusEnum, isInstrumentStopped } from '@/enum';

const columns: ColumnDefinition<PoolDetail> = [
  {
    key: 'instrument',
    dataKey: 'instrument',
    title: '证券代码',
    width: 100,
  },
  {
    key: 'instrumentName',
    dataKey: 'instrumentName',
    title: '证券名称',
    width: 100,
  },
  {
    key: 'risePercent',
    dataKey: 'risePercent',
    title: '涨跌幅',
    sortable: true,
    width: 100,
    cellRenderer: ({ cellData }) => {
      return (
        <span class={cellData > 0 ? 'c-[var(--g-red)]' : 'c-[var(--g-green)]'}>
          {Utils.formatNumber(cellData, { percent: true, prefix: true })}
        </span>
      );
    },
  },
  {
    key: 'poolName',
    dataKey: 'poolName',
    title: '股票池',
    width: 100,
  },
  {
    key: 'position',
    dataKey: 'position',
    title: '仓位',
    width: 100,
    cellRenderer: ({ cellData }) => {
      return <span>{Utils.formatNumber(cellData, { percent: true })}</span>;
    },
  },
  {
    key: 'poolId',
    dataKey: 'poolId',
    title: '启停',
    width: 100,
    cellRenderer: ({ rowData }: { rowData: PoolDetail }) => {
      return (
        <el-switch
          modelValue={
            isInstrumentStopped(rowData) ? InstrumentStatusEnum.已停止 : InstrumentStatusEnum.运行中
          }
          active-value={1}
          inactive-value={0}
          before-change={() => beforeChange(rowData)}
        />
      );
    },
  },
  {
    key: 'status',
    dataKey: 'status',
    title: '状态',
    width: 100,
    cellRenderer: ({ cellData }) => {
      const text = InstrumentStatusEnum[cellData];
      return <span>{text}</span>;
    },
  },
];

const rowActions: RowAction<PoolDetail>[] = [
  {
    label: '删除',
    onClick: row => {
      ElMessageBox.confirm('确认删除', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        const { err, msg } = await PoolService.deleteMonitorPoolInstrument(row.id);
        if (err === 0) {
          ElMessage.success('删除成功');
          monitorInfos.value = monitorInfos.value.filter(x => x.id !== row.id);
        } else {
          ElMessage.error(msg || '删除失败');
        }
      });
    },
    color: 'var(--g-red)',
  },
];

const monitorInfos = shallowRef<PoolDetail[]>([]);

onMounted(() => {
  getData();
});

const getData = async () => {
  const { err, data } = await PoolService.getMonitorPool();
  if (err === 0 && data) {
    monitorInfos.value = data;
  }
};

const handleConfig = () => {};
const handleStart = () => {};
const handleStop = () => {};
const handleRefresh = () => {};
/** 进行启停操作，成功返回true,否则返回false */
const beforeChange = async (rowData: PoolDetail) => {
  const { err, msg } = await PoolService[
    isInstrumentStopped(rowData) ? 'startPoolInstrument' : 'stopPoolInstrument'
  ](rowData.id);
  if (err === 0) {
    Misc.putRow(
      {
        ...rowData,
        status: isInstrumentStopped(rowData)
          ? InstrumentStatusEnum.运行中
          : InstrumentStatusEnum.已停止,
      },
      monitorInfos,
    );
    return true;
  } else {
    ElMessage.error(msg || '操作失败');
    return false;
  }
};
</script>

<template>
  <div flex="~ col">
    <div flex aic jcsb px-16 h-32>
      <div text-16 font-bold>监控池</div>
      <div>
        <el-button size="small" @click="handleConfig">
          <i mr-4 fs-14 i-mdi-cog-outline />
          设置
        </el-button>
        <el-button size="small" @click="handleStart">
          <i mr-4 fs-14 i-mdi-motion-play-outline />
          一键启动
        </el-button>
        <el-button size="small" @click="handleStop">
          <i mr-4 fs-14 i-mdi-motion-pause-outline />
          一键停止
        </el-button>
        <el-button size="small" @click="handleRefresh">
          <i mr-4 fs-14 i-mdi-refresh />
          刷新
        </el-button>
      </div>
    </div>
    <VirtualizedTable
      flex-1
      min-h-1
      :data="monitorInfos"
      :columns="columns"
      :row-actions="rowActions"
    ></VirtualizedTable>
  </div>
</template>

<style scoped></style>
